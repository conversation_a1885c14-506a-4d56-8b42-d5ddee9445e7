# Favicon Setup Instructions

## Required Favicon Files

Add the following files to the `assets/` directory:

### 1. **favicon.ico** (16x16, 32x32, 48x48)
- Classic favicon for browsers
- Should contain your initials "SC" or a simple logo

### 2. **apple-touch-icon.png** (180x180)
- For iOS devices when adding to home screen
- Should be a clean, simple design

### 3. **portfolio-preview.jpg** (1200x630)
- For social media sharing (Open Graph)
- Should showcase your portfolio design

## How to Create Favicons

### Option 1: Online Favicon Generators
1. Visit [favicon.io](https://favicon.io/) or [realfavicongenerator.net](https://realfavicongenerator.net/)
2. Upload your logo or use text generator with "SC"
3. Download the generated files
4. Place them in the `assets/` folder

### Option 2: Design Tools
1. Create a 512x512 PNG with your initials "SC"
2. Use the same futuristic gradient as your portfolio
3. Convert to required sizes using online tools

### Option 3: Simple Text Favicon
```css
/* You can also create a simple CSS-based favicon */
background: linear-gradient(135deg, #00d4ff 0%, #7c3aed 100%);
color: white;
font-family: 'Segoe UI', sans-serif;
font-weight: bold;
```

## Design Recommendations

### Colors
- Use the same gradient as your portfolio: `#00d4ff` to `#7c3aed`
- Ensure good contrast for readability

### Content
- Your initials "SC" work well for favicon
- Keep it simple and recognizable at small sizes
- Maintain consistency with your portfolio theme

### Sizes
- **favicon.ico**: 16x16, 32x32, 48x48 (multi-size ICO file)
- **apple-touch-icon.png**: 180x180
- **portfolio-preview.jpg**: 1200x630 (for social sharing)

## Testing

After adding favicons:
1. Clear browser cache
2. Reload your portfolio
3. Check browser tab for favicon
4. Test on mobile devices
5. Verify social media preview with [Facebook Debugger](https://developers.facebook.com/tools/debug/)

## Current Status

The HTML is already configured to use these favicons:
```html
<link rel="icon" type="image/x-icon" href="assets/favicon.ico">
<link rel="apple-touch-icon" sizes="180x180" href="assets/apple-touch-icon.png">
```

Just add the actual files to complete the setup!
