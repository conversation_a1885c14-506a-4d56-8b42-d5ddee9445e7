## CS Projects


# 1. Job Portal - Full Stack Application Using MongoDB
Job Portal - Full Stack Application Using MongoDB
May 2025 - Jun 2025May 2025 - Jun 2025
Indian Institute of Technology, Guwahati logo
Associated with Indian Institute of Technology, Guwahati
Associated with Indian Institute of Technology, Guwahati
Full-stack job portal with React frontend and Node.js backend with Mongoose MongoDB Database
Full-stack job portal with React frontend and Node.js backend with Mongoose MongoDB Database
Skills: MongoDB · Mongoose ODM · Node.js · React.js
Skills: MongoDB · Mongoose ODM · Node.js · React.js

Demo - JobPortal System using Mongoose MongoDB || College ProjectDemo - JobPortal System using Mongoose MongoDB || College Project

Github : https://github.com/p4r1ch4y/job-portal
Live : https://github.com/p4r1ch4y/job-portal







# 2. LifeWeeks - Your Life in 4,000 Weeks
LifeWeeks - Your Life in 4,000 Weeks
Jun 2025 - Jun 2025Jun 2025 - Jun 2025
Indian Institute of Technology, Guwahati logo
Associated with Indian Institute of Technology, Guwahati
Associated with Indian Institute of Technology, Guwahati
A modern, AI-powered interactive timeline application that visualizes your life week by week. Built during Anveshan Jun 2025 hackathon with a focus on polished UX, advanced AI features, and meaningful personal insights.
A modern, AI-powered interactive timeline application that visualizes your life week by week. Built during Anveshan Jun 2025 hackathon with a focus on polished UX, advanced AI features, and meaningful personal insights.
Skills: PostgreSQL · SQL · Next.js · React.js · Tailwind CSS · Amazon Web Services (AWS)
Skills: PostgreSQL · SQL · Next.js · React.js · Tailwind CSS · Amazon Web Services (AWS) , Higgingface, AI API 

LifeWeeks - AI powered LifeInWeeks App Built During Anveshan June 25 Hackathon | Group PresentationLifeWeeks - AI powered LifeInWeeks App Built During Anveshan June 25 Hackathon | Group Presentation
The What How Why The What How Why  YouTube Presentation



GitHub - p4r1ch4y/FunctionForce_LifeInWeeks: It’s hard to grasp how quickly weeks fly by. A clear, visual timeline helps people see their life at a glance, celebrate memories, and understand the world events that shaped those moments via this ai powered toolGitHub - p4r1ch4y/FunctionForce_LifeInWeeks: It’s hard to grasp how quickly weeks fly by. A clear, visual timeline helps people see their life at a glance, celebrate memories, and understand the world events that shaped those moments via this ai powered tool
Other contributorsOther contributors


Github : https://github.com/p4r1ch4y/FunctionForce_LifeInWeeks
Live Url : https://lifeweeks.vercel.app/



# 3. SkillSync - Candidate and Recruiter matching reimagined
SkillSync - Candidate and Recruiter matching reimagined
Apr 2025 - Apr 2025Apr 2025 - Apr 2025
Indian Institute of Technology, Guwahati logo
Associated with Indian Institute of Technology, Guwahati
Associated with Indian Institute of Technology, Guwahati
About
SkillSync - Candidate and Recruiter matching reimagined, built during hackathon xto10x
About SkillSync - Candidate and Recruiter matching reimagined, built during hackathon xto10x
Skills: Team Leadership · Teamwork · hackathon · Node.js · SQL
Skills: Team Leadership · Teamwork · hackathon · Node.js · SQL

Introduction and insights of our website : Skillsync | xto10x 48hrs Hackathon by Masai with IITGIntroduction and insights of our website : Skillsync | xto10x 48hrs Hackathon by Masai with IITG

GitHub - p4r1ch4y/skillsync: SkillSync - Candidate and Recruiter matching reimagined, build during hackathon xto10x https://skillsynced.vercel.app/GitHub - p4r1ch4y/skillsync: SkillSync - Candidate and Recruiter matching reimagined, build during hackathon xto10x https://skillsynced.vercel.app/
Other contributorsOther contributors



## Elecltrical Engineering Projects


# 1 Fabrication of Inverter
Fabrication of Inverter
Nov 2022 - Apr 2023Nov 2022 - Apr 2023
a group project on how anyone can build a homemade ac-dc inverter. And they can use that as a portable inverter.
a group project on how anyone can build a homemade ac-dc inverter. And they can use that as a portable inverter.
Skills: Electrical Engineering · Electronics · Circuit · Analog Circuit Design · Circuit Design
Skills: Electrical Engineering · Electronics · Circuit · Analog Circuit Design · Circuit Design


# 2. Time Delay Relay circuit using IC555
Time Delay Relay circuit using IC555
Oct 2022 - Nov 2022Oct 2022 - Nov 2022
Skills: Electrical Engineering · Electronics · Microchip PIC · Microcontroller · 8051 Microcontroller · Integrated Circuits (IC)