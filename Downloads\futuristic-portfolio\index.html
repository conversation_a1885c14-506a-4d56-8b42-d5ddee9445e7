<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Primary Meta Tags - UPDATED FOR NEW NARRATIVE -->
    <title><PERSON><PERSON>hur<PERSON> - Software Engineer & Deep Tech Student</title>
    <meta name="description" content="A results-driven Software Engineer with a foundation in Web3 and Developer Relations. Currently deepening expertise in Protocol Security, Quantitative Systems, and Quantum Computing through elite training programs.">
    <meta name="keywords" content="<PERSON><PERSON> Choudhury, Software Engineer, Protocol Security, Solana, Web3, Blockchain, AI, Machine Learning, Quantum Computing, Systems Design, IIT Guwahati">
    <meta name="author" content="Subrata Choudhury">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://p4r1ch4y.github.io/">

    <!-- Open Graph Meta Tags - UPDATED -->
    <meta property="og:title" content="Subrata Choudhury - Software Engineer & Deep Tech Student">
    <meta property="og:description" content="A proven builder now in a hyper-growth phase deepening expertise in Protocol Security (School of Solana), Quantitative Systems, and Quantum Computing (IBM Qiskit).">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://p4r1ch4y.github.io/">
    <!-- NOTE: Replace with a real screenshot of your new portfolio -->
    <meta property="og:image" content="https://p4r1ch4y.github.io/assets/portfolio-preview.jpg"> 
    <meta property="og:site_name" content="Subrata Choudhury Portfolio">

    <!-- Twitter Card Meta Tags - UPDATED -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@iamcsubrata">
    <meta name="twitter:creator" content="@iamcsubrata">
    <meta name="twitter:title" content="Subrata Choudhury - Software Engineer & Deep Tech Student">
    <meta name="twitter:description" content="Exploring the intersection of AI, Web3 Security, and the future of computation.">
    <!-- NOTE: Replace with a real screenshot -->
    <meta name="twitter:image" content="https://p4r1ch4y.github.io/assets/portfolio-preview.jpg">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/apple-touch-icon.png">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Preload Critical Resources -->
    <link rel="preload" href="style.css" as="style">
    <link rel="preload" href="app.js" as="script">

    <!-- Structured Data - UPDATED -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Person",
        "name": "Subrata Choudhury",
        "jobTitle": "Software Engineer",
        "description": "Software Engineer with experience in Web3 and Developer Relations, currently undergoing advanced training in Protocol Security, Quantitative Systems, and Quantum Computing.",
        "url": "https://p4r1ch4y.github.io/",
        "email": "<EMAIL>",
        "address": {
            "@type": "PostalAddress",
            "addressLocality": "Kharagpur",
            "addressRegion": "West Bengal",
            "addressCountry": "India"
        },
        "sameAs": [
            "https://github.com/p4r1ch4y",
            "https://linkedin.com/in/iamcsubrata",
            "https://twitter.com/iamcsubrata"
        ],
        "knowsAbout": [
            "Software Engineering",
            "Web3 Technologies",
            "Blockchain Security",
            "Solana",
            "Systems Design",
            "Quantum Computing",
            "Developer Relations"
        ],
        "alumniOf": {
            "@type": "EducationalOrganization",
            "name": "IIT Guwahati"
        }
    }
    </script>
</head>
<body>
    <!-- Skip to Content Link for Accessibility -->
    <a href="#main-content" class="skip-to-content">Skip to main content</a>

    <!-- Loading Screen (Unchanged, it's good) -->
    <div id="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">SC</div>
            <div class="loading-bar"><div class="loading-progress"></div></div>
            <div class="loading-text">Initializing Portfolio...</div>
        </div>
    </div>

    <!-- Particle Background (Unchanged) -->
    <div id="particles-container"></div>

    <!-- Navigation (Unchanged, functional) -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo"><span class="logo-text">Subrata Choudhury</span></div>
            <ul class="nav-menu" id="nav-menu" role="navigation" aria-label="Main navigation">
                <li><a href="#home" class="nav-link" aria-label="Go to home section">Home</a></li>
                <li><a href="#about" class="nav-link" aria-label="Go to about section">About</a></li>
                <li><a href="#skills" class="nav-link" aria-label="Go to skills section">Skills</a></li>
                <li><a href="#projects" class="nav-link" aria-label="Go to projects section">Projects</a></li>
                <li><a href="#experience" class="nav-link" aria-label="Go to experience section">Experience</a></li>
                <li><a href="#certifications" class="nav-link" aria-label="Go to certifications section">Certifications</a></li>
                <li><a href="#contact" class="nav-link" aria-label="Go to contact section">Contact</a></li>
            </ul>
            <button class="nav-toggle" id="nav-toggle" aria-label="Toggle mobile navigation" aria-expanded="false">
                <span class="bar" aria-hidden="true"></span>
                <span class="bar" aria-hidden="true"></span>
                <span class="bar" aria-hidden="true"></span>
            </button>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content">
        <!-- Hero Section - FULLY REDESIGNED NARRATIVE -->
        <section id="home" class="hero" aria-label="Introduction and hero section">
            <div class="hero-content">
                <div class="hero-image">
                    <div class="profile-circle"><div class="profile-inner">SC</div></div>
                </div>
                <h1 class="hero-title">
                    <!-- The typewriter text now shows your new trajectory -->
                    <span class="typewriter" id="typewriter"></span>
                    <span class="cursor">|</span>
                </h1>
                <!-- The hero subtitle is now sharp and focused -->
                <p class="hero-subtitle">Software Engineer | Web3 & Deep Tech Enthusiast</p>
                <!-- The hero description explains your current, impressive state -->
                <p class="hero-description">
                    A proven builder with experience in full-stack development and Web3, now in a hyper-growth phase deepening expertise in Protocol Security (School of Solana) and Quantum Computing (IBM Qiskit).
                </p>
                <div class="hero-buttons">
                    <a href="#projects" class="btn btn--primary">View My Work</a>
                    <!-- NOTE: Add the correct path to your PDF resume -->
                    <a href="assets/Subrata_Choudhury_Resume.pdf" class="btn btn--outline" id="download-resume">Download Resume</a>
                </div>
                <div class="hero-socials">
                    <a href="https://github.com/p4r1ch4y" target="_blank" class="social-link"><i class="fab fa-github"></i></a>
                    <a href="https://linkedin.com/in/iamcsubrata" target="_blank" class="social-link"><i class="fab fa-linkedin"></i></a>
                    <a href="https://twitter.com/iamcsubrata" target="_blank" class="social-link"><i class="fab fa-twitter"></i></a>
                    <!-- NOTE: Removed broken Discord link. Add it back if you have a relevant server. -->
                </div>
            </div>
        </section>

    <!-- About Section - Placeholder text updated -->
    <section id="about" class="about">
        <div class="container">
            <h2 class="section-title">About Me</h2>
            <div class="about-grid">
                 <div class="about-info">
                    <!-- ...your info items... -->
                </div>
                <div class="about-interests">
                    <!-- ...your interest cards... -->
                </div>
                <div class="about-achievements">
                    <h3>Key Achievements</h3>
                    <div class="achievement-cards">
                        <!-- ...other achievement cards... -->
                        <div class="achievement-card">
                            <i class="fas fa-chart-line"></i>
                            <h4>Community Growth</h4>
                            <!-- NOTE: Replaced placeholder XX% with real number from resume -->
                            <p>Increased active community engagement by 35% at Theia Studios</p>
                        </div>
                         <!-- ...other achievement cards... -->
                    </div>
                </div>
                 <div class="about-stats">
                    <!-- ...your stats items... -->
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section - COMPLETELY REDESIGNED & RESTRUCTURED -->
    <section id="skills" class="skills">
        <div class="container">
            <h2 class="section-title">Skills & Technologies</h2>
            <div class="skills-grid-new"> <!-- Using a new class for new styling -->
                <div class="skill-category-new">
                    <h3>Programming Languages</h3>
                    <ul class="skill-list">
                        <li>Rust</li>
                        <li>Python</li>
                        <li>TypeScript/JavaScript</li>
                        <li>C++</li>
                        <li>SQL</li>
                    </ul>
                </div>
                <div class="skill-category-new">
                    <h3>Blockchain & Web3</h3>
                    <ul class="skill-list">
                        <li>Solana</li>
                        <li>Anchor Framework</li>
                        <li>Smart Contract Auditing</li>
                        <li>Protocol Design</li>
                        <li>Web3.js</li>
                    </ul>
                </div>
                 <div class="skill-category-new">
                    <h3>Backend & Cloud</h3>
                    <ul class="skill-list">
                        <li>Node.js / Express.js</li>
                        <li>JWT & Authentication</li>
                        <li>REST APIs</li>
                        <li>PostgreSQL</li>
                        <li>MongoDB</li>
                    </ul>
                </div>
                <div class="skill-category-new">
                    <h3>Frontend</h3>
                    <ul class="skill-list">
                        <li>React / Next.js 14</li>
                        <li>HTML5 & CSS3</li>
                        <li>Tailwind CSS</li>
                        <li>Vercel</li>
                    </ul>
                </div>
                <div class="skill-category-new">
                    <h3>Specializations</h3>
                    <ul class="skill-list">
                        <li>System Design</li>
                        <li>High-Performance Computing</li>
                        <li>Ethical Hacking</li>
                        <li>Quantitative Analysis</li>
                        <li>Developer Relations</li>
                    </ul>
                </div>
                <div class="skill-category-new">
                    <h3>Core Competencies</h3>
                    <ul class="skill-list">
                        <li>Secure Software Development</li>
                        <li>Technical Writing</li>
                        <li>Community Management</li>
                        <li>Problem Solving</li>
                        <li>Adaptability</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Projects Section - Updated with image path advice -->
    <section id="projects" class="projects">
        <div class="container">
            <h2 class="section-title">Featured Projects</h2>
            <div class="project-filters">
                <button class="filter-btn active" data-filter="all">All</button>
                <button class="filter-btn" data-filter="ai">CS/AI</button>
                <button class="filter-btn" data-filter="web3">Web3</button>
                <button class="filter-btn" data-filter="iot">Hardware</button>
            </div>
            <div class="projects-grid">
                <!-- Project Card Example with new image path -->
                <div class="project-card" data-category="web3">
                    <div class="project-image">
                        <!-- NOTE: Replace this with a real screenshot of your work, e.g., 'assets/projects/stylus.jpg' -->
                        <img src="assets/projects/stylus.jpg" alt="Stylus SDK Technical Articles">
                        <!-- ...overlay... -->
                    </div>
                     <!-- ...project content... -->
                </div>
                 <!-- ...all other project cards... -->
            </div>
        </div>
    </section>

    <!-- Experience Section - Updated placeholders -->
    <section id="experience" class="experience">
        <div class="container">
            <h2 class="section-title">Experience & Education</h2>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-dot active"></div>
                    <div class="timeline-content">
                        <h3>Student in Computer Science Engineering</h3>
                        <!-- NOTE: Replaced "IIT X" with real name -->
                        <h4>IIT Guwahati (Credit Linked Program)</h4>
                        <span class="timeline-period">May 2024 - Sep 2025</span>
                        <p>Pursuing advanced Computer Science Engineering with focus on AI, secure systems, and high-performance software. Specializing in System Design, algorithms, and S-SDLC principles.</p>
                         <!-- ...tech tags... -->
                    </div>
                </div>
                <!-- ...other timeline items... -->
                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <h3>Student in Electrical Engineering</h3>
                         <!-- NOTE: Replaced "XXX Govt. College" with real name -->
                        <h4>Haji Md. Serafat Mondal Govt. Polytechnic</h4>
                        <span class="timeline-period">Aug 2020 - Aug 2023</span>
                        <p>Comprehensive electrical engineering education covering circuit design, power systems, control theory, and embedded systems.</p>
                        <!-- ...tech tags... -->
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Blog Section (Unchanged, functionally good) -->
    <!-- Certifications Section (Reordered for Impact) -->
     <section id="certifications" class="certifications">
        <div class="container">
            <h2 class="section-title">Certifications & Training</h2>
            <div class="certifications-grid">
                <div class="certification-card">
                    <div class="cert-icon"><i class="fas fa-atom"></i></div> <!-- Qiskit Icon -->
                    <h3>Qiskit Global Summer School</h3>
                    <p>IBM Quantum</p>
                    <span class="cert-date">Completed Jul 2025</span>
                </div>
                <div class="certification-card">
                    <div class="cert-icon"><i class="fab fa-teamspeak"></i></div> <!-- Placeholder for Solana/Acqee -->
                    <h3>School of Solana (Security Track)</h3>
                    <p>Ackee Blockchain Security</p>
                    <span class="cert-date">In Progress (Est. Sep 2025)</span>
                </div>
                <div class="certification-card">
                    <div class="cert-icon"><i class="fas fa-users"></i></div>
                    <h3>DevRel Uni Cohort 6</h3>
                    <p>Developer Relations Essentials</p>
                    <span class="cert-date">Completed Jan 2025</span>
                </div>
                 <div class="certification-card">
                    <div class="cert-icon"><i class="fas fa-certificate"></i></div>
                    <h3>Azure AI Fundamentals (AI-900)</h3>
                    <p>Microsoft</p>
                    <span class="cert-date">Completed Jun 2025</span>
                </div>
                <div class="certification-card">
                    <div class="cert-icon"><i class="fas fa-wave-square"></i></div>
                    <h3>Quantum Computing Training</h3>
                    <p>C-DAC & IIT Roorkee</p>
                    <span class="cert-date">Completed Jun 2025</span>
                </div>
                <div class="certification-card">
                    <div class="cert-icon"><i class="fas fa-trophy"></i></div>
                    <h3>Anveshan Hackathon Winner</h3>
                    <p>Software Development Track</p>
                    <span class="cert-date">Jun 2025</span>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Contact Section (Unchanged) -->
    <!-- Footer (Unchanged) -->
    <!-- Project Modal (Unchanged) -->

    <!-- Ensure your app.js is loaded -->
    <script src="app.js"></script>
    <script>
        // JS for the typewriter effect - UPDATED WITH NEW STRINGS
        document.addEventListener('DOMContentLoaded', function() {
            const typewriterElement = document.getElementById('typewriter');
            if(typewriterElement) {
                const words = ["Software Engineer.", "Web3 Developer.", "Deep Tech Strategist."];
                let wordIndex = 0;
                let charIndex = 0;
                let isDeleting = false;
        
                function type() {
                    const currentWord = words[wordIndex];
                    let displayText = '';
        
                    if (isDeleting) {
                        displayText = currentWord.substring(0, charIndex - 1);
                        charIndex--;
                    } else {
                        displayText = currentWord.substring(0, charIndex + 1);
                        charIndex++;
                    }
        
                    typewriterElement.textContent = displayText;
        
                    let typeSpeed = isDeleting ? 100 : 200;
        
                    if (!isDeleting && charIndex === currentWord.length) {
                        typeSpeed = 2000; // Pause after typing word
                        isDeleting = true;
                    } else if (isDeleting && charIndex === 0) {
                        isDeleting = false;
                        wordIndex = (wordIndex + 1) % words.length;
                        typeSpeed = 500; // Pause before typing new word
                    }
        
                    setTimeout(type, typeSpeed);
                }
                type();
            }
        });
    </script>
</body>
</html>