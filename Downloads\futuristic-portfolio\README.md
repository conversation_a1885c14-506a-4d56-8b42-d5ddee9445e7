# 🚀 Subrata Choudhury - Futuristic Portfolio

A modern, responsive portfolio website showcasing expertise in Electrical Engineering, Computer Science, AI, and Web3 technologies.

## 🌟 Features

- **Futuristic Design**: Modern glassmorphism UI with particle animations
- **Responsive Layout**: Optimized for all devices and screen sizes
- **Interactive Elements**: Smooth animations, typewriter effects, and hover interactions
- **Dark/Light Mode**: Automatic theme switching based on user preference
- **Performance Optimized**: Fast loading with efficient animations
- **SEO Ready**: Proper meta tags and structured data
- **GitHub Pages Compatible**: Ready for deployment

## 🛠️ Technologies Used

### Frontend
- **HTML5**: Semantic markup and accessibility features
- **CSS3**: Modern CSS with custom properties and animations
- **JavaScript (ES6+)**: Interactive functionality and animations
- **Font Awesome**: Icon library for social media and UI elements

### Design Features
- **Glassmorphism**: Modern glass-like UI elements
- **Particle System**: Dynamic background animations
- **Smooth Scrolling**: Enhanced navigation experience
- **Intersection Observer**: Efficient scroll-based animations

## 📁 Project Structure

```
portfolio/
├── index.html              # Main HTML file
├── style.css               # Comprehensive CSS styles
├── app.js                  # JavaScript functionality
├── 404.html                # Custom 404 error page
├── _config.yml             # GitHub Pages configuration
├── README.md               # Project documentation
├── assets/                 # Static assets directory
│   └── favicon
|   └── My_Resume.pdf       # Assets 
└── *.csv                   # Project planning files
```

## 🚀 Quick Start

### Local Development
1. Clone the repository
2. Open `index.html` in your browser
3. For development server, use Live Server extension in VS Code

### GitHub Pages Deployment
1. Push to your GitHub repository
2. Go to Settings > Pages
3. Select source branch (usually `main`)
4. Your site will be available at `https://p4r1ch4y.github.io/portfolio`

## 📝 Customization

### Personal Information
Update the following in `index.html`:
- Name and title in hero section
- Contact information
- Social media links
- Education and experience details

### Projects
Modify the `projects` array in `app.js` to add your own projects:
```javascript
const projects = [
    {
        title: "Your Project Title",
        description: "Project description...",
        technologies: ["Tech1", "Tech2"],
        features: ["Feature 1", "Feature 2"],
        challenges: "Challenges faced...",
        liveUrl: "https://your-demo.com",
        githubUrl: "https://github.com/your-repo"
    }
];
```

### Styling
- Colors: Modify CSS custom properties in `:root`
- Animations: Adjust timing and effects in CSS and JavaScript
- Layout: Update grid and flexbox properties

## 📱 Responsive Design

The portfolio is fully responsive with breakpoints:
- Mobile: < 768px
- Tablet: 768px - 1024px
- Desktop: > 1024px

## ⚡ Performance Features

- **Lazy Loading**: Images and animations load when needed
- **Debounced Events**: Optimized scroll and resize handlers
- **Efficient Animations**: CSS transforms and GPU acceleration
- **Minimal Dependencies**: Lightweight external resources

## 🔧 Browser Support

- Chrome (recommended)
- Firefox
- Safari
- Edge
- Mobile browsers

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Feel free to fork this project and customize it for your own portfolio!

## 📞 Contact

- **Email**: <EMAIL>
- **LinkedIn**: [linkedin.com/in/iamcsubrata](https://linkedin.com/in/iamcsubrata)
- **GitHub**: [github.com/p4r1ch4y](https://github.com/p4r1ch4y)
- **Twitter**: [twitter.com/iamcsubrata](https://twitter.com/iamcsubrata)

---

⭐ If you found this portfolio template helpful, please give it a star!
