name: Deploy Portfolio to GitHub Pages

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
# However, do NOT cancel in-progress runs as we want to allow these production deployments to complete.
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  # Build job
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        
      - name: Setup Pages
        uses: actions/configure-pages@v4
        
      - name: Validate HTML
        run: |
          # Install HTML validator
          sudo apt-get update
          sudo apt-get install -y tidy
          
          # Validate HTML files (continue on error for now)
          tidy -q -e index.html || true
          tidy -q -e 404.html || true
          
      - name: Optimize Images (if any)
        run: |
          # This would optimize images if you add any
          echo "Image optimization step - add imagemin or similar tool here"
          
      - name: Build with <PERSON><PERSON><PERSON>
        uses: actions/jekyll-build-pages@v1
        with:
          source: ./
          destination: ./_site
          
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3

  # Deployment job
  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
        
      - name: Post-deployment notification
        run: |
          echo "Portfolio deployed successfully to GitHub Pages!"
          echo "URL: ${{ steps.deployment.outputs.page_url }}"
