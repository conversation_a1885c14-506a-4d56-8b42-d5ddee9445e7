# 🚀 Deployment Guide for GitHub Pages

## Quick Start Deployment

### 1. **Repository Setup**
```bash
# Initialize git repository (if not already done)
git init

# Add all files
git add .

# Commit changes
git commit -m "Initial portfolio commit"

# Add remote repository
git remote add origin https://github.com/p4r1ch4y/your-portfolio-repo.git

# Push to GitHub
git push -u origin main
```

### 2. **GitHub Pages Configuration**
1. Go to your repository on GitHub
2. Navigate to **Settings** → **Pages**
3. Under **Source**, select **GitHub Actions**
4. The deployment workflow will automatically trigger

### 3. **Custom Domain (Optional)**
If you want to use a custom domain:

1. Add a `CNAME` file to your repository root:
```bash
echo "yourdomain.com" > CNAME
```

2. Configure DNS settings with your domain provider:
   - Add a CNAME record pointing to `p4r1ch4y.github.io`
   - Or add A records pointing to GitHub Pages IPs

## 📁 File Structure for GitHub Pages

```
portfolio/
├── index.html              # Main page (required)
├── 404.html                # Custom 404 page
├── _config.yml             # Jekyll configuration
├── style.css               # Styles
├── app.js                  # JavaScript
├── README.md               # Documentation
├── DEPLOYMENT.md           # This file
├── assets/                 # Static assets
│   ├── favicon.ico
│   ├── apple-touch-icon.png
│   └── Subrata_Choudhury_Resume.pdf
├── .github/
│   └── workflows/
│       └── deploy.yml      # GitHub Actions workflow
└── *.csv                   # Project files (excluded from build)
```

## 🔧 Configuration Files

### _config.yml
Update the following values in `_config.yml`:
```yaml
url: "https://p4r1ch4y.github.io"
baseurl: "/portfolio"  # Update this!
```

### GitHub Actions Workflow
The `.github/workflows/deploy.yml` file handles:
- HTML validation
- Jekyll build process
- Automatic deployment
- Error handling

## 🌐 URL Structure

After deployment, your portfolio will be available at:
- **GitHub Pages URL**: `https://p4r1ch4y.github.io/portfolio/`
- **Custom Domain** (if configured): `https://portfolio.com/`

## 📝 Pre-Deployment Checklist

### Required Files
- [ ] `index.html` exists and is valid
- [ ] `_config.yml` is configured correctly
- [ ] All links are relative or absolute (no broken links)
- [ ] Images have proper alt text
- [ ] Resume file is added to `assets/` folder

### Content Updates
- [ ] Personal information is accurate
- [ ] Social media links are correct
- [ ] Project links point to actual repositories/demos
- [ ] Contact information is up to date
- [ ] Skills and experience reflect current status

### Performance & SEO
- [ ] Meta tags are properly configured
- [ ] Structured data is included
- [ ] Images are optimized
- [ ] CSS and JS are minified (optional)

## 🔍 Testing Before Deployment

### Local Testing
```bash
# Serve locally using Python
python -m http.server 8000

# Or using Node.js
npx http-server

# Or using Live Server in VS Code
```

### Validation
- **HTML**: Use W3C Markup Validator
- **CSS**: Use W3C CSS Validator
- **Accessibility**: Use WAVE or axe DevTools
- **Performance**: Use Lighthouse in Chrome DevTools

## 🚨 Common Issues & Solutions

### Issue: 404 on GitHub Pages
**Solution**: Check that `index.html` is in the root directory and repository is public.

### Issue: CSS/JS not loading
**Solution**: Ensure all paths are relative (e.g., `./style.css` not `/style.css`).

### Issue: Images not displaying
**Solution**: Check image paths and ensure images are committed to repository.

### Issue: Custom domain not working
**Solution**: Verify DNS settings and ensure CNAME file is in repository root.

## 📊 Monitoring & Analytics

### GitHub Pages Analytics
- Monitor deployment status in Actions tab
- Check Pages settings for build status
- Review commit history for changes

### Optional: Add Google Analytics
Add to `<head>` section of `index.html`:
```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_TRACKING_ID');
</script>
```

## 🔄 Continuous Deployment

The GitHub Actions workflow automatically:
1. Triggers on push to main/master branch
2. Validates HTML and CSS
3. Builds the site with Jekyll
4. Deploys to GitHub Pages
5. Provides deployment URL

## 📞 Support

If you encounter issues:
1. Check GitHub Actions logs for error details
2. Verify all files are properly committed
3. Ensure repository is public (for free GitHub Pages)
4. Review GitHub Pages documentation

## 🎯 Next Steps After Deployment

1. **Share your portfolio**: Add the URL to your resume and social profiles
2. **Monitor performance**: Use Google PageSpeed Insights
3. **Update regularly**: Keep projects and skills current
4. **Backup**: Regularly backup your repository
5. **Analytics**: Monitor visitor traffic and engagement

---

🎉 **Congratulations!** Your portfolio is now live on GitHub Pages!
